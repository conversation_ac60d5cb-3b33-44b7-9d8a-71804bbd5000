<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>四象限图实现方案对比</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .method-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .method-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .method-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .method-description {
            color: #666;
            margin-bottom: 20px;
            font-size: 0.95em;
            line-height: 1.6;
        }

        /* 方案1: CSS Grid 样式 */
        .quadrant-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 2px;
            border: 2px solid #ddd;
            border-radius: 12px;
            overflow: hidden;
            min-height: 300px;
            position: relative;
        }

        .quadrant-grid::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #ddd;
            z-index: 1;
        }

        .quadrant-grid::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #ddd;
            z-index: 1;
        }

        .quadrant {
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            border: 2px solid transparent;
        }

        .quadrant:hover {
            transform: scale(1.02);
        }

        .quadrant.active {
            border-color: #667eea;
            box-shadow: inset 0 0 20px rgba(102, 126, 234, 0.2);
        }

        .quadrant.active::before {
            content: '★';
            position: absolute;
            top: 8px;
            right: 12px;
            color: #667eea;
            font-size: 1.2em;
        }

        .quadrant-icon {
            font-size: 2em;
            margin-bottom: 8px;
        }

        .quadrant-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .quadrant-desc {
            font-size: 0.85em;
            color: #666;
            margin-bottom: 8px;
        }

        .quadrant-features {
            font-size: 0.75em;
            color: #888;
            line-height: 1.4;
        }

        /* 象限特定颜色 */
        .quadrant-secure { background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); }
        .quadrant-anxious { background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); }
        .quadrant-avoidant { background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); }
        .quadrant-chaotic { background: linear-gradient(135deg, #e2d9f3 0%, #d1a3e0 100%); }

        /* 方案2: SVG 样式 */
        .quadrant-svg-container {
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 12px;
            overflow: hidden;
        }

        .quadrant-svg {
            width: 100%;
            height: 100%;
        }

        .quadrant-label {
            font-size: 14px;
            font-weight: 600;
            fill: #2c3e50;
        }

        .axis-label {
            font-size: 12px;
            fill: #666;
        }

        /* 方案3: Canvas 样式 */
        .quadrant-canvas {
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 12px;
            display: block;
        }

        /* 方案4: 交互式HTML 样式 */
        .interactive-quadrant {
            position: relative;
        }

        .quadrant-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 10px;
            min-height: 300px;
        }

        .quadrant-item {
            padding: 20px;
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .quadrant-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .quadrant-item.active {
            transform: scale(1.05);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        }

        .quadrant-item.secure { background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); }
        .quadrant-item.anxious { background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); }
        .quadrant-item.avoidant { background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); }
        .quadrant-item.chaotic { background: linear-gradient(135deg, #e2d9f3 0%, #d1a3e0 100%); }

        .quadrant-content h4 {
            font-size: 1.2em;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .quadrant-content p {
            font-size: 0.9em;
            color: #555;
            margin-bottom: 8px;
        }

        .score-range {
            font-size: 0.8em;
            color: #777;
            font-style: italic;
        }

        .user-result-display {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }

        .result-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #667eea;
        }

        /* 控制面板 */
        .control-panel {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .score-controls {
            display: flex;
            gap: 30px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        .score-control {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .score-control label {
            font-weight: 600;
            color: #2c3e50;
        }

        .score-control input {
            width: 200px;
            height: 40px;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 0 15px;
            font-size: 1.1em;
            text-align: center;
        }

        .score-display {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
        }

        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .score-controls {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 四象限图实现方案对比</h1>
            <p>ECR依恋类型四象限图的四种不同实现方式</p>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="score-controls">
                <div class="score-control">
                    <label for="anxietyScore">依恋焦虑得分</label>
                    <input type="range" id="anxietyScore" min="1" max="7" step="0.1" value="3.2">
                    <div class="score-display" id="anxietyDisplay">3.2</div>
                </div>
                <div class="score-control">
                    <label for="avoidanceScore">依恋回避得分</label>
                    <input type="range" id="avoidanceScore" min="1" max="7" step="0.1" value="2.8">
                    <div class="score-display" id="avoidanceDisplay">2.8</div>
                </div>
            </div>
        </div>

        <div class="comparison-grid">
            <!-- 方案1: CSS Grid布局 -->
            <div class="method-card">
                <div class="method-title">
                    <span>🎨</span> 方案1: CSS Grid布局
                </div>
                <div class="method-description">
                    使用CSS Grid创建静态四象限布局，直观展示四种依恋类型特征。优点：简单直观、响应式好、无依赖。
                </div>
                
                <div class="quadrant-grid" id="gridQuadrant">
                    <div class="quadrant quadrant-secure" data-type="secure">
                        <div class="quadrant-icon">🛡️</div>
                        <div class="quadrant-title">安全型</div>
                        <div class="quadrant-desc">低焦虑 + 低回避</div>
                        <div class="quadrant-features">
                            关系稳定和谐<br>
                            情感表达自然<br>
                            信任伴侣
                        </div>
                    </div>
                    
                    <div class="quadrant quadrant-anxious" data-type="anxious">
                        <div class="quadrant-icon">💔</div>
                        <div class="quadrant-title">焦虑型</div>
                        <div class="quadrant-desc">高焦虑 + 低回避</div>
                        <div class="quadrant-features">
                            担心被抛弃<br>
                            需要频繁确认<br>
                            情感依赖强
                        </div>
                    </div>
                    
                    <div class="quadrant quadrant-avoidant" data-type="avoidant">
                        <div class="quadrant-icon">🚪</div>
                        <div class="quadrant-title">回避型</div>
                        <div class="quadrant-desc">低焦虑 + 高回避</div>
                        <div class="quadrant-features">
                            保持情感距离<br>
                            独立性强<br>
                            不易亲密
                        </div>
                    </div>
                    
                    <div class="quadrant quadrant-chaotic" data-type="chaotic">
                        <div class="quadrant-icon">🌪️</div>
                        <div class="quadrant-title">混乱型</div>
                        <div class="quadrant-desc">高焦虑 + 高回避</div>
                        <div class="quadrant-features">
                            情感矛盾<br>
                            关系不稳定<br>
                            行为不一致
                        </div>
                    </div>
                </div>
            </div>

            <!-- 方案2: SVG绘制 -->
            <div class="method-card">
                <div class="method-title">
                    <span>📐</span> 方案2: SVG矢量图
                </div>
                <div class="method-description">
                    使用SVG创建精确的坐标系统，支持矢量缩放和动画效果。优点：矢量图形、可缩放、支持动画。
                </div>
                
                <div class="quadrant-svg-container">
                    <svg width="100%" height="100%" viewBox="0 0 400 300" class="quadrant-svg" id="svgQuadrant">
                        <!-- 背景象限 -->
                        <rect x="0" y="0" width="200" height="150" fill="#d4edda" opacity="0.6"/>
                        <rect x="200" y="0" width="200" height="150" fill="#fff3cd" opacity="0.6"/>
                        <rect x="0" y="150" width="200" height="150" fill="#d1ecf1" opacity="0.6"/>
                        <rect x="200" y="150" width="200" height="150" fill="#e2d9f3" opacity="0.6"/>
                        
                        <!-- 坐标轴 -->
                        <line x1="200" y1="0" x2="200" y2="300" stroke="#666" stroke-width="2"/>
                        <line x1="0" y1="150" x2="400" y2="150" stroke="#666" stroke-width="2"/>
                        
                        <!-- 象限标签 -->
                        <text x="100" y="25" text-anchor="middle" class="quadrant-label">🛡️ 安全型</text>
                        <text x="300" y="25" text-anchor="middle" class="quadrant-label">💔 焦虑型</text>
                        <text x="100" y="280" text-anchor="middle" class="quadrant-label">🚪 回避型</text>
                        <text x="300" y="280" text-anchor="middle" class="quadrant-label">🌪️ 混乱型</text>
                        
                        <!-- 用户位置点 -->
                        <circle id="userPointSVG" cx="200" cy="150" r="8" fill="#667eea" stroke="#fff" stroke-width="3">
                            <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
                        </circle>
                        
                        <!-- 坐标轴标签 -->
                        <text x="380" y="145" class="axis-label">回避程度 →</text>
                        <text x="10" y="15" class="axis-label">焦虑程度 ↑</text>
                    </svg>
                </div>
            </div>
        </div>

        <div class="comparison-grid">
            <!-- 方案3: Canvas绘制 -->
            <div class="method-card">
                <div class="method-title">
                    <span>🎮</span> 方案3: Canvas动态绘制
                </div>
                <div class="method-description">
                    使用Canvas API创建高性能的动态图形，支持复杂动画和交互效果。优点：高性能、动画丰富、完全自定义。
                </div>

                <canvas id="quadrantCanvas" class="quadrant-canvas" width="400" height="300"></canvas>
            </div>

            <!-- 方案4: 交互式HTML -->
            <div class="method-card">
                <div class="method-title">
                    <span>🎪</span> 方案4: 交互式HTML
                </div>
                <div class="method-description">
                    使用HTML和CSS创建用户友好的交互式四象限。优点：用户友好、易于理解、可访问性好。
                </div>

                <div class="interactive-quadrant">
                    <div class="quadrant-wrapper" id="interactiveQuadrant">
                        <div class="quadrant-item secure" data-type="secure">
                            <div class="quadrant-content">
                                <h4>🛡️ 安全型</h4>
                                <p>稳定和谐的关系模式</p>
                                <div class="score-range">焦虑: 1-4, 回避: 1-4</div>
                            </div>
                        </div>

                        <div class="quadrant-item anxious" data-type="anxious">
                            <div class="quadrant-content">
                                <h4>💔 焦虑型</h4>
                                <p>渴望亲密但担心失去</p>
                                <div class="score-range">焦虑: 4-7, 回避: 1-4</div>
                            </div>
                        </div>

                        <div class="quadrant-item avoidant" data-type="avoidant">
                            <div class="quadrant-content">
                                <h4>🚪 回避型</h4>
                                <p>独立但难以建立亲密</p>
                                <div class="score-range">焦虑: 1-4, 回避: 4-7</div>
                            </div>
                        </div>

                        <div class="quadrant-item chaotic" data-type="chaotic">
                            <div class="quadrant-content">
                                <h4>🌪️ 混乱型</h4>
                                <p>矛盾复杂的情感模式</p>
                                <div class="score-range">焦虑: 4-7, 回避: 4-7</div>
                            </div>
                        </div>
                    </div>

                    <div class="user-result-display">
                        <div class="result-badge">
                            <span class="badge-icon">⭐</span>
                            <span class="badge-text" id="userTypeDisplay">您的类型: 安全型</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentScores = {
            anxiety: 3.2,
            avoidance: 2.8
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeControls();
            updateAllQuadrants();
        });

        // 初始化控制面板
        function initializeControls() {
            const anxietySlider = document.getElementById('anxietyScore');
            const avoidanceSlider = document.getElementById('avoidanceScore');
            const anxietyDisplay = document.getElementById('anxietyDisplay');
            const avoidanceDisplay = document.getElementById('avoidanceDisplay');

            anxietySlider.addEventListener('input', function() {
                currentScores.anxiety = parseFloat(this.value);
                anxietyDisplay.textContent = this.value;
                updateAllQuadrants();
            });

            avoidanceSlider.addEventListener('input', function() {
                currentScores.avoidance = parseFloat(this.value);
                avoidanceDisplay.textContent = this.value;
                updateAllQuadrants();
            });
        }

        // 确定依恋类型
        function getAttachmentType(anxiety, avoidance) {
            if (anxiety < 4 && avoidance < 4) return 'secure';
            if (anxiety >= 4 && avoidance < 4) return 'anxious';
            if (anxiety < 4 && avoidance >= 4) return 'avoidant';
            return 'chaotic';
        }

        // 获取类型中文名称
        function getTypeName(type) {
            const names = {
                'secure': '安全型',
                'anxious': '焦虑型',
                'avoidant': '回避型',
                'chaotic': '混乱型'
            };
            return names[type] || '未知类型';
        }

        // 更新所有四象限显示
        function updateAllQuadrants() {
            const userType = getAttachmentType(currentScores.anxiety, currentScores.avoidance);

            updateGridQuadrant(userType);
            updateSVGQuadrant();
            updateCanvasQuadrant();
            updateInteractiveQuadrant(userType);
        }

        // 方案1: 更新CSS Grid四象限
        function updateGridQuadrant(userType) {
            const quadrants = document.querySelectorAll('#gridQuadrant .quadrant');
            quadrants.forEach(q => q.classList.remove('active'));

            const activeQuadrant = document.querySelector(`#gridQuadrant [data-type="${userType}"]`);
            if (activeQuadrant) {
                activeQuadrant.classList.add('active');
            }
        }

        // 方案2: 更新SVG四象限
        function updateSVGQuadrant() {
            const userPoint = document.getElementById('userPointSVG');
            if (userPoint) {
                const x = (currentScores.avoidance / 7) * 400;
                const y = 300 - (currentScores.anxiety / 7) * 300;
                userPoint.setAttribute('cx', x);
                userPoint.setAttribute('cy', y);
            }
        }

        // 方案3: Canvas绘制四象限
        function updateCanvasQuadrant() {
            const canvas = document.getElementById('quadrantCanvas');
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            // 清空画布
            ctx.clearRect(0, 0, width, height);

            // 绘制象限背景
            const quadrants = [
                { x: 0, y: 0, color: '#d4edda', label: '🛡️ 安全型' },
                { x: 200, y: 0, color: '#fff3cd', label: '💔 焦虑型' },
                { x: 0, y: 150, color: '#d1ecf1', label: '🚪 回避型' },
                { x: 200, y: 150, color: '#e2d9f3', label: '🌪️ 混乱型' }
            ];

            quadrants.forEach(quad => {
                ctx.fillStyle = quad.color;
                ctx.globalAlpha = 0.6;
                ctx.fillRect(quad.x, quad.y, 200, 150);

                // 绘制标签
                ctx.globalAlpha = 1;
                ctx.fillStyle = '#333';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(quad.label, quad.x + 100, quad.y + 25);
            });

            // 绘制坐标轴
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(200, 0);
            ctx.lineTo(200, 300);
            ctx.moveTo(0, 150);
            ctx.lineTo(400, 150);
            ctx.stroke();

            // 绘制坐标轴标签
            ctx.fillStyle = '#666';
            ctx.font = '12px Arial';
            ctx.textAlign = 'right';
            ctx.fillText('回避程度 →', 390, 145);
            ctx.textAlign = 'left';
            ctx.fillText('焦虑程度 ↑', 10, 15);

            // 绘制用户位置
            const userX = (currentScores.avoidance / 7) * 400;
            const userY = 300 - (currentScores.anxiety / 7) * 300;

            // 绘制脉动效果
            ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
            ctx.beginPath();
            ctx.arc(userX, userY, 15, 0, 2 * Math.PI);
            ctx.fill();

            // 绘制主圆点
            ctx.fillStyle = '#667eea';
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(userX, userY, 8, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();
        }

        // 方案4: 更新交互式HTML四象限
        function updateInteractiveQuadrant(userType) {
            const quadrants = document.querySelectorAll('#interactiveQuadrant .quadrant-item');
            quadrants.forEach(q => q.classList.remove('active'));

            const activeQuadrant = document.querySelector(`#interactiveQuadrant [data-type="${userType}"]`);
            if (activeQuadrant) {
                activeQuadrant.classList.add('active');
            }

            // 更新类型显示
            const typeDisplay = document.getElementById('userTypeDisplay');
            if (typeDisplay) {
                typeDisplay.textContent = `您的类型: ${getTypeName(userType)}`;
            }
        }

        // 添加Canvas动画效果
        let animationId;
        function startCanvasAnimation() {
            let pulseRadius = 15;
            let growing = true;

            function animate() {
                updateCanvasQuadrant();

                const canvas = document.getElementById('quadrantCanvas');
                const ctx = canvas.getContext('2d');
                const userX = (currentScores.avoidance / 7) * 400;
                const userY = 300 - (currentScores.anxiety / 7) * 300;

                // 绘制动态脉动效果
                ctx.fillStyle = `rgba(102, 126, 234, ${0.3 - (pulseRadius - 15) * 0.02})`;
                ctx.beginPath();
                ctx.arc(userX, userY, pulseRadius, 0, 2 * Math.PI);
                ctx.fill();

                // 更新脉动半径
                if (growing) {
                    pulseRadius += 0.5;
                    if (pulseRadius >= 25) growing = false;
                } else {
                    pulseRadius -= 0.5;
                    if (pulseRadius <= 15) growing = true;
                }

                animationId = requestAnimationFrame(animate);
            }

            animate();
        }

        // 启动Canvas动画
        setTimeout(startCanvasAnimation, 1000);
    </script>
</body>
</html>
