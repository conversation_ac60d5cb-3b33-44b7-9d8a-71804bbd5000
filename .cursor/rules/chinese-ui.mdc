---
alwaysApply: true
---

# 中文界面开发规则

## 语言和本地化要求

### 中文字符处理
**强制要求：**
- 所有用户界面文本必须使用简体中文
- 专业术语采用标准学术翻译（如"依恋理论"而非"依恋关系理论"）
- 避免过度使用英文术语，除非是通用的技术词汇
- 数字和英文字符与中文之间适当添加空格

### 文案编写标准
**用户体验文案：**
- 使用温和、积极的语言，避免负面或判断性表述
- 说明文字要简洁明了，避免冗长的句子
- 按钮文字使用动词形式（"开始测评"而非"测评开始"）
- 错误提示要具体且提供解决方案

**心理测评特定文案：**
- 测评说明要专业但易懂
- 避免使用可能引起焦虑的词汇
- 结果描述采用中性、客观的语言
- 提供建设性的建议而非诊断性结论

## 排版和设计规范

### 中文字体系统
**推荐字体栈：**
```css
font-family: 
    /* 系统字体优先 */
    -apple-system, BlinkMacSystemFont,
    /* 中文字体 */
    'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC',
    'Microsoft YaHei', 'WenQuanYi Micro Hei',
    /* 英文fallback */
    'Segoe UI', Roboto, Arial,
    /* 通用fallback */
    sans-serif;
```

### 文字排版
**行高和间距：**
- 正文行高：1.6-1.8（中文推荐值）
- 标题行高：1.2-1.4
- 段落间距：1em-1.5em
- 字间距：正常（避免过度拉伸）

**文字大小指导：**
```css
/* 推荐的中文字体大小 */
.text-lg { font-size: 18px; }     /* 大标题 */
.text-base { font-size: 16px; }   /* 正文（推荐最小值） */
.text-sm { font-size: 14px; }     /* 辅助文字 */
.text-xs { font-size: 12px; }     /* 注释文字（谨慎使用） */
```

### 颜色和对比度
**可读性要求：**
- 正文文字对比度至少4.5:1
- 大字体（18px+）至少3:1
- 重要信息使用高对比度
- 避免单纯依赖颜色传达信息

**推荐色彩方案：**
```css
:root {
    /* 主要文字颜色 */
    --text-primary: #1a1a1a;
    --text-secondary: #666666;
    --text-disabled: #999999;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f5f5f5;
    
    /* 强调色 */
    --accent-blue: #1890ff;
    --accent-green: #52c41a;
    --accent-red: #ff4d4f;
}
```

## 交互设计规范

### 输入法适配
**中文输入优化：**
```javascript
// 监听中文输入状态
let isComposing = false;

input.addEventListener('compositionstart', () => {
    isComposing = true;
});

input.addEventListener('compositionend', () => {
    isComposing = false;
    // 输入完成后的处理逻辑
    handleInput();
});

input.addEventListener('input', (e) => {
    if (!isComposing) {
        // 非中文输入时的实时处理
        handleInput();
    }
});
```

### 表单设计
**中文表单最佳实践：**
- 标签文字清晰，使用冒号结尾
- 必填项使用红色星号（*）标识
- 错误提示放在输入框下方
- 提供清晰的格式说明

**示例模板：**
```html
<div class="form-item">
    <label for="input-id">字段名称：<span class="required">*</span></label>
    <input type="text" id="input-id" placeholder="请输入内容">
    <div class="form-help">格式说明或帮助文本</div>
    <div class="form-error">错误提示信息</div>
</div>
```

### 按钮和操作
**中文按钮设计：**
- 使用动作导向的文字（"提交答案"、"重新开始"）
- 主要操作使用醒目颜色
- 次要操作使用较淡的颜色
- 危险操作（如删除）使用红色系

## 内容组织规范

### 信息层次
**内容结构：**
1. **主标题**：清晰概括页面目的
2. **副标题**：补充说明或当前状态
3. **正文内容**：详细信息，分段呈现
4. **操作区域**：明确的行动指引

### 导航和流程
**用户引导：**
- 提供清晰的步骤指示（"第1步，共3步"）
- 使用面包屑导航显示当前位置
- 重要操作前提供确认提示
- 完成流程后给予明确反馈

## 响应式适配

### 移动端中文优化
**小屏幕适配：**
- 增加触摸目标尺寸（最小44px×44px）
- 优化文字大小（移动端最小16px）
- 简化导航结构
- 考虑横竖屏切换的布局变化

### 可访问性支持
**无障碍访问：**
```html
<!-- 屏幕阅读器支持 -->
<button aria-label="开始心理测评">开始测评</button>

<!-- 状态说明 -->
<div aria-live="polite" id="status">正在加载测评内容...</div>

<!-- 表单关联 -->
<label for="name-input">您的姓名：</label>
<input id="name-input" aria-describedby="name-help">
<div id="name-help">姓名将用于生成个性化报告</div>
```

## 性能和加载

### 中文字体优化
**字体加载策略：**
```css
/* 字体预加载 */
@font-face {
    font-family: 'CustomFont';
    src: url('font.woff2') format('woff2');
    font-display: swap; /* 避免文字闪烁 */
}

/* 渐进式字体加载 */
.text {
    font-family: 'CustomFont', 'PingFang SC', sans-serif;
}
```

### 内容加载体验
**加载状态设计：**
- 使用骨架屏而非纯色占位
- 提供具体的加载进度（如"正在加载测评题目 3/10"）
- 避免长时间的空白等待
- 为慢网络环境优化加载策略

## 错误处理和反馈

### 中文错误信息
**错误提示原则：**
- 使用用户能理解的语言
- 提供具体的错误原因
- 给出明确的解决步骤
- 保持友好和帮助性的语调

**示例错误信息：**
```javascript
const errorMessages = {
    required: '此项为必填内容',
    email: '请输入正确的邮箱格式',
    minLength: '内容长度不能少于{min}个字符',
    network: '网络连接异常，请检查网络后重试',
    timeout: '操作超时，请刷新页面重试'
};
```

### 成功反馈
**积极反馈设计：**
- 及时给予操作确认
- 使用积极正面的语言
- 提供下一步操作建议
- 适当使用动画增强体验

## 测试和验证

### 中文环境测试
**必须测试项：**
- 不同字体下的显示效果
- 长短文本的排版表现
- 输入法兼容性
- 多语言混排效果
- 不同设备上的中文渲染

### 用户体验验证
**可用性检查：**
- 文案是否易于理解
- 操作流程是否符合中文用户习惯
- 错误处理是否友好
- 性能在中文环境下是否良好

## 维护和更新

### 内容管理
**文案维护：**
- 建立统一的术语表
- 定期检查文案的准确性
- 收集用户反馈进行改进
- 保持品牌语调的一致性

### 技术债务
**避免常见问题：**
- 硬编码的中文文本
- 不当的字符编码处理
- 忽略中文输入法的特殊性
- 缺乏对中文排版的考虑

