---
alwaysApply: true
---

# ECR心理测评项目开发规则

## 项目概述
当前项目是ECR（Experience in Close Relationships）亲密关系经历量表心理测评应用，包含：
- 心理学量表测试系统
- 依恋理论四种类型评估（安全型、焦虑型、回避型、混乱型）
- 数据可视化和报告生成
- 专业心理测评界面

主要文件：[code_1752652404242.html](mdc:code_1752652404242.html)

## 心理测评特定要求

### 数据准确性原则
**强制要求：**
- 所有心理学量表计分必须精确无误
- 分值计算、反向计分、维度划分必须严格按照学术标准
- 绝不允许篡改或简化心理测评的科学性

### 用户隐私保护
**隐私要求：**
- 测评数据不得存储到外部服务器
- 所有计算在客户端完成
- 结果报告不包含可识别个人身份的信息
- 测评过程中确保用户数据安全

### 专业术语使用
**术语标准：**
- 使用标准心理学术语（依恋理论、焦虑维度、回避维度）
- 避免过度医学化的表述
- 结果解释采用科学、客观、平和的语言
- 不进行诊断性陈述，仅提供倾向性描述

### 用户体验设计
**UX原则：**
- 测评过程应循序渐进，避免引起用户焦虑
- 问题呈现简洁明了，避免歧义
- 进度指示清晰，让用户了解测评进展
- 结果展示图文并茂，易于理解

### 测评流程标准
**流程要求：**
1. **指导说明**：清晰的测评说明和注意事项
2. **问题呈现**：一次一题，避免信息过载
3. **答题验证**：确保每题都有作答
4. **结果计算**：实时或即时计算分数
5. **报告生成**：结构化的测评报告
6. **重测功能**：允许用户重新开始测评

## 代码质量要求

### HTML结构
- 语义化标签使用
- 无障碍访问支持（aria-label、alt属性等）
- 响应式设计确保各设备兼容

### CSS样式
- 使用现代CSS特性
- 保持设计的专业性和科学感
- 色彩搭配温和，避免刺激性颜色
- 适当的视觉层次和留白

### JavaScript功能
- 模块化代码结构
- 错误处理和边界情况考虑
- 性能优化（避免不必要的DOM操作）
- 数据验证和计算准确性保证

## 测评内容指导

### ECR量表特定要求
- 焦虑维度和回避维度的准确计算
- 反向计分题目的正确处理
- 四种依恋类型的准确划分
- 标准化分数的转换和解释

### 结果报告结构
1. **总体得分**：焦虑和回避维度分数
2. **依恋类型**：基于分数的类型判定
3. **特征描述**：各维度的详细解释
4. **发展建议**：积极、建设性的建议
5. **可视化图表**：直观的分数展示

## 开发注意事项

### 禁止行为
- 不得修改已验证的心理学计分公式
- 不得添加未经验证的测评内容
- 不得简化或省略重要的心理学概念解释
- 不得使用可能引起用户不适的语言或图像

### 推荐实践
- 参考权威心理学文献和量表手册
- 保持与国际标准的一致性
- 定期检查计算逻辑的准确性
- 优化用户交互体验

## 质量保证
所有涉及心理测评的修改都必须：
1. 验证计分算法的准确性
2. 检查结果解释的科学性
3. 确保用户体验的专业性
4. 测试各种输入情况的处理

