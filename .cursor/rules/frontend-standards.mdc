---
globs: *.html,*.css,*.js
---

# 前端开发标准规则

## HTML开发标准

### 语义化标记
- 使用恰当的HTML5语义标签（`<header>`, `<main>`, `<section>`, `<article>`, `<aside>`, `<footer>`）
- 表单元素必须包含相应的`<label>`标签
- 图片必须包含有意义的`alt`属性
- 使用`<button>`而不是`<div>`或`<span>`来创建可点击元素

### 无障碍访问（A11y）
**强制要求：**
- 所有交互元素必须可以通过键盘访问
- 使用适当的ARIA属性（`aria-label`, `aria-describedby`, `role`等）
- 确保充足的颜色对比度（至少4.5:1）
- 为动态内容提供屏幕阅读器支持

### 代码结构
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>具体而描述性的标题</title>
    <!-- 外部资源链接 -->
    <!-- 内联样式 -->
</head>
<body>
    <!-- 结构化内容 -->
    <!-- JavaScript在body底部 -->
</body>
</html>
```

## CSS开发标准

### 现代CSS特性
- 使用CSS Grid和Flexbox进行布局
- 利用CSS自定义属性（CSS变量）提高可维护性
- 使用`clamp()`、`min()`、`max()`实现响应式设计
- 采用逻辑属性（`margin-inline`, `padding-block`等）

### 样式组织
**推荐结构：**
```css
/* 1. CSS Reset/Normalize */
/* 2. CSS自定义属性 */
/* 3. 基础样式 */
/* 4. 布局组件 */
/* 5. UI组件 */
/* 6. 工具类 */
/* 7. 响应式媒体查询 */
```

### 命名规范
- 使用语义化的类名（避免表现层命名如`.red-text`）
- 采用BEM方法论或一致的命名约定
- 使用kebab-case命名（`.button-primary`, `.modal-overlay`）

### 响应式设计
```css
/* 移动优先方法 */
.component {
    /* 基础样式（移动设备） */
}

@media (min-width: 768px) {
    .component {
        /* 平板样式 */
    }
}

@media (min-width: 1024px) {
    .component {
        /* 桌面样式 */
    }
}
```

## JavaScript开发标准

### ES6+特性使用
- 使用`const`和`let`替代`var`
- 采用箭头函数（适当时）
- 使用模板字符串进行字符串拼接
- 利用解构赋值和扩展运算符
- 使用`async/await`处理异步操作

### 代码组织
**推荐结构：**
```javascript
// 1. 常量声明
const CONFIG = {
    // 配置选项
};

// 2. 工具函数
function utilities() {
    // 辅助函数
}

// 3. 主要功能模块
class MainComponent {
    // 核心逻辑
}

// 4. 事件监听器
document.addEventListener('DOMContentLoaded', init);

// 5. 初始化函数
function init() {
    // 应用启动逻辑
}
```

### 错误处理
```javascript
// 使用try-catch处理可能的错误
async function fetchData() {
    try {
        const response = await fetch('/api/data');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('获取数据失败:', error);
        // 用户友好的错误处理
        showErrorMessage('加载数据时出现问题，请稍后重试');
    }
}
```

### 性能优化
- 避免不必要的DOM查询（缓存DOM引用）
- 使用事件委托处理动态元素
- 实现防抖（debounce）和节流（throttle）
- 延迟加载非关键资源

## 中文项目特殊考虑

### 字体设置
```css
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 
                 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 
                 'Helvetica Neue', Helvetica, Arial, sans-serif;
}
```

### 文本处理
- 设置适当的行高（中文推荐1.6-1.8）
- 使用`word-break: break-all`或`word-wrap: break-word`处理长文本
- 考虑中英文混排的排版效果

### 输入法支持
```javascript
// 处理中文输入法的composition事件
input.addEventListener('compositionstart', function() {
    isComposing = true;
});

input.addEventListener('compositionend', function() {
    isComposing = false;
    // 在输入完成后执行逻辑
});
```

## 代码质量保证

### 必须检查项
- HTML验证（W3C Markup Validator）
- CSS语法检查
- JavaScript语法和逻辑检查
- 浏览器兼容性测试
- 移动设备适配测试

### 性能要求
- 首屏加载时间 < 3秒
- 交互响应时间 < 100ms
- 避免重排和重绘
- 优化资源加载策略

### 安全考虑
- 输入验证和清理
- 避免内联事件处理器
- 使用CSP（Content Security Policy）
- 防止XSS攻击

## 调试和测试

### 开发工具使用
- 浏览器开发者工具的有效利用
- Console.log的规范使用（生产环境移除）
- 网络面板监控资源加载
- 性能面板分析运行时性能

### 测试策略
- 单元测试关键功能
- 集成测试用户流程
- 跨浏览器测试
- 无障碍访问测试

## 最佳实践总结

1. **渐进增强**：从基础功能开始，逐步添加高级特性
2. **优雅降级**：确保在旧浏览器中基本功能可用
3. **模块化开发**：将功能拆分为可重用的模块
4. **文档注释**：为复杂逻辑添加清晰的注释
5. **版本控制**：使用有意义的提交信息

