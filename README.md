# ECR亲密关系经历量表

一个专业的心理测评系统，用于评估个体在亲密关系中的依恋模式。

## 项目简介

ECR（Experiences in Close Relationships）亲密关系经历量表是一个基于Web的心理测评工具，帮助用户了解自己在亲密关系中的依恋类型和行为模式。

## 功能特点

- 📊 **科学测评**: 基于心理学研究的标准化量表
- 🎨 **现代界面**: 简洁美观的用户界面设计
- 📱 **响应式设计**: 支持各种设备和屏幕尺寸
- 📈 **可视化报告**: 直观的图表展示测评结果
- 🔒 **隐私保护**: 本地处理，不存储个人数据

## 技术栈

- HTML5
- CSS3 (使用现代CSS特性)
- JavaScript (原生JS)
- Chart.js (图表库)
- Font Awesome (图标库)

## 项目结构

```
ECR/
├── code_1752652404242.html    # 主要的测评页面
├── quadrant-comparison.html    # 四象限对比页面
├── .cursor/                    # Cursor IDE 配置
├── .vscode/                    # VS Code 配置
└── README.md                   # 项目说明文档
```

## 使用方法

1. 打开 `code_1752652404242.html` 文件
2. 按照页面提示完成测评
3. 查看个性化的测评报告

## 在线访问

项目已部署到 GitHub Pages，您可以直接访问：
https://ystyleb.github.io/ECR-psychology-assessment/

## 本地开发

```bash
# 克隆项目
git clone https://github.com/ystyleb/ECR-psychology-assessment.git

# 进入项目目录
cd ECR-psychology-assessment

# 使用本地服务器打开（推荐）
python -m http.server 8000
# 或者
npx serve
```

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有任何问题或建议，请通过GitHub Issues联系我们。
